import { Routes } from '@angular/router';

import { LoginComponent } from './auth/login/login.component';
import { SignupComponent } from './auth/signup/signup.component';

import { DashboardComponent } from './features/dashboard/dashboard/dashboard.component';
import { AbsencesComponent } from './features/absences/absences.component';
import { RequestsComponent } from './features/requests/requests.component';
import { TimeComponent } from './features/time/time.component';
import { TeamComponent } from './features/team/team.component';

export const routes: Routes = [
  { path: '', redirectTo: 'login', pathMatch: 'full' },

  { path: 'login', component: LoginComponent },
  { path: 'signup', component: SignupComponent },

  {
    path: '',
    component: DashboardComponent, // => page d’accueil après login (tu peux mettre un shell ici si tu veux)
    children: [
      { path: 'dashboard', component: DashboardComponent },
      { path: 'requests', component: RequestsComponent },
      { path: 'time', component: TimeComponent },
      { path: 'absences', component: AbsencesComponent },
      { path: 'team', component: TeamComponent },
    ],
  },

  { path: '**', redirectTo: 'login' },
];
