<h1>Suiv<PERSON> du te<PERSON></h1>

<div class="grid">
  <mat-card>
    <h3>Tracker actuel</h3>
    <div class="timer">{{ display }}</div>
    <div class="btns">
      <button mat-raised-button color="primary" (click)="start()" [disabled]="running">D<PERSON>marrer</button>
      <button mat-stroked-button color="warn" (click)="stop()" [disabled]="!running">Arrêter</button>
      <button mat-button (click)="reset()" [disabled]="running || elapsed===0">Réinitialiser</button>
    </div>

    <h4>Entrée rapide</h4>
    <form class="row" (ngSubmit)="addQuick()">
      <mat-form-field appearance="outline" class="w">
        <mat-label>Projet</mat-label>
        <input matInput [(ngModel)]="quick.project" name="project">
      </mat-form-field>
      <mat-form-field appearance="outline" class="w">
        <mat-label>Tâche</mat-label>
        <input matInput [(ngModel)]="quick.task" name="task">
      </mat-form-field>
      <button mat-raised-button color="primary">Enregistrer</button>
    </form>
  </mat-card>

  <mat-card>
    <h3>Cette semaine</h3>
    <div class="big">{{ weekTotal() | number:'1.0-1' }}h</div>
    <div class="muted">Objectif : 40h</div>
  </mat-card>
</div>

<mat-card>
  <h3>Activités récentes</h3>
  <table mat-table [dataSource]="entries" class="mat-elevation-z0 full">
    <ng-container matColumnDef="date">
      <th mat-header-cell *matHeaderCellDef>Date</th>
      <td mat-cell *matCellDef="let e">{{ e.date }}</td>
    </ng-container>
    <ng-container matColumnDef="project">
      <th mat-header-cell *matHeaderCellDef>Projet</th>
      <td mat-cell *matCellDef="let e">{{ e.project }}</td>
    </ng-container>
    <ng-container matColumnDef="task">
      <th mat-header-cell *matHeaderCellDef>Tâche</th>
      <td mat-cell *matCellDef="let e">{{ e.task }}</td>
    </ng-container>
    <ng-container matColumnDef="hours">
      <th mat-header-cell *matHeaderCellDef>Heures</th>
      <td mat-cell *matCellDef="let e">{{ e.hours }}</td>
    </ng-container>
    <tr mat-header-row *matHeaderRowDef="['date','project','task','hours']"></tr>
    <tr mat-row *matRowDef="let row; columns: ['date','project','task','hours'];"></tr>
  </table>
</mat-card>

<style>
  .grid{display:grid;grid-template-columns:2fr 1fr;gap:16px;margin-bottom:16px}
  @media (max-width:900px){.grid{grid-template-columns:1fr}}
  .timer{font-size:42px;font-weight:800;margin:6px 0 10px}
  .btns button{margin-right:8px}
  .row{display:flex;gap:12px;align-items:flex-end}
  .w{flex:1}
  .big{font-size:34px;font-weight:800}
  .muted{color:#6b7280}
  .full{width:100%}
</style>
