<h1>Équipe</h1>

<mat-card class="mb">
  <div class="row">
    <button mat-raised-button color="primary" (click)="add()"><mat-icon>person_add</mat-icon> Ajouter un membre</button>
    <span class="flex"></span>
    <mat-form-field appearance="outline" style="width:320px">
      <mat-label>Rechercher</mat-label>
      <input matInput [(ngModel)]="q" placeholder="nom, email…">
    </mat-form-field>
  </div>
</mat-card>

<div class="grid">
  <mat-card *ngFor="let m of filtered">
    <div class="head">
      <div class="avatar">{{ m.initials }}</div>
      <div>
        <div class="name">{{ m.name }}</div>
        <div class="muted">{{ m.email }}</div>
      </div>
      <span class="flex"></span>
      <mat-chip [selected]="true" [color]="m.role==='Admin' ? 'warn' : m.role==='Manager' ? 'primary' : undefined">
        {{ m.role }}
      </mat-chip>
    </div>

    <div class="foot">
      <mat-icon [style.color]="m.online ? '#2e7d32' : '#9ca3af'">fiber_manual_record</mat-icon>
      <span>{{ m.online ? 'En ligne' : 'Hors ligne' }}</span>
    </div>
  </mat-card>
</div>

<style>
  .mb{margin-bottom:16px}
  .row{display:flex;gap:12px;align-items:center}
  .flex{flex:1 1 auto}
  .grid{display:grid;grid-template-columns:repeat(3,minmax(0,1fr));gap:16px}
  @media (max-width:1100px){.grid{grid-template-columns:repeat(2,1fr)}}
  @media (max-width:700px){.grid{grid-template-columns:1fr}}
  .head{display:flex;gap:12px;align-items:center;margin-bottom:6px}
  .avatar{width:40px;height:40px;border-radius:50%;background:#5b7cff;color:#fff;
          display:flex;align-items:center;justify-content:center;font-weight:800}
  .name{font-weight:700}
  .muted{color:#6b7280}
  .foot{display:flex;gap:6px;align-items:center;color:#6b7280}
</style>
