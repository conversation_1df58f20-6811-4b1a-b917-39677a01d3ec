import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { environment } from '../../environments/environment';
import { tap } from 'rxjs/operators';

type LoginResponse = { token: string };

@Injectable({ providedIn: 'root' })
export class AuthService {
  private key = 'token';
  constructor(private http: HttpClient) {}

  signup(data: { username: string; email: string; password: string }) {
    return this.http.post(`${environment.api}/auth/signup`, data);
  }

  login(username: string, password: string) {
    return this.http.post<LoginResponse>(`${environment.api}/auth/login`, { username, password })
      .pipe(tap(res => this.saveToken(res.token)));
  }

  saveToken(t: string) { localStorage.setItem(this.key, t); }
  get token() { return localStorage.getItem(this.key); }
  get isAuthenticated() { return !!this.token; }
  logout() { localStorage.removeItem(this.key); }
}
