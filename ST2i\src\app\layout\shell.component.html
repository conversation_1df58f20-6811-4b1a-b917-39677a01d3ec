<mat-sidenav-container class="shell">
  <mat-sidenav mode="side" opened class="side">
    <div class="brand">
      <div class="avatar">S</div>
      <div class="title">STE2I</div>
      <small>Système de Maintenance SI</small>
    </div>
    <mat-nav-list>
      <a mat-list-item routerLink="/dashboard" routerLinkActive="active">
        <mat-icon>dashboard</mat-icon><span>Tableau de bord</span>
      </a>
      <a mat-list-item routerLink="/requests" routerLinkActive="active">
        <mat-icon>assignment</mat-icon><span>Demandes</span>
      </a>
      <a mat-list-item routerLink="/time" routerLinkActive="active">
        <mat-icon>schedule</mat-icon><span>Suivi temps</span>
      </a>
      <a mat-list-item routerLink="/absences" routerLinkActive="active">
        <mat-icon>event_busy</mat-icon><span>Absences</span>
      </a>
      <a mat-list-item routerLink="/team" routerLinkActive="active">
        <mat-icon>group</mat-icon><span>Équipe</span>
      </a>
    </mat-nav-list>
  </mat-sidenav>
  <mat-sidenav-content>
    <mat-toolbar color="primary" class="top">
      <span class="flex"></span>
      <button mat-icon-button><mat-icon>notifications</mat-icon></button>
      <button mat-raised-button (click)="logout()">Déconnexion</button>
    </mat-toolbar>
    <div class="page"><router-outlet></router-outlet></div>
  </mat-sidenav-content>
</mat-sidenav-container>
