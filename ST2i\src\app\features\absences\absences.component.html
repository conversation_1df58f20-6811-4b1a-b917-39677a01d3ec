<h1>Absences</h1>

<mat-card class="mb">
  <div class="row">
    <button mat-raised-button color="primary"><mat-icon>add</mat-icon> Nouvelle absence</button>
    <span class="flex"></span>
    <mat-form-field appearance="outline" style="width:320px">
      <mat-label>Filtrer (mock)</mat-label>
      <input matInput placeholder="Nom, type, statut…">
    </mat-form-field>
  </div>
</mat-card>

<mat-card>
  <table mat-table [dataSource]="items" class="mat-elevation-z0 full">
    <ng-container matColumnDef="employee">
      <th mat-header-cell *matHeaderCellDef>Employé</th>
      <td mat-cell *matCellDef="let i">{{ i.employee }}</td>
    </ng-container>

    <ng-container matColumnDef="type">
      <th mat-header-cell *matHeaderCellDef>Type</th>
      <td mat-cell *matCellDef="let i">{{ i.type }}</td>
    </ng-container>

    <ng-container matColumnDef="period">
      <th mat-header-cell *matHeaderCellDef>Période</th>
      <td mat-cell *matCellDef="let i">Du {{ i.from }} au {{ i.to }}</td>
    </ng-container>

    <ng-container matColumnDef="days">
      <th mat-header-cell *matHeaderCellDef>Jours</th>
      <td mat-cell *matCellDef="let i">{{ i.days }}</td>
    </ng-container>

    <ng-container matColumnDef="status">
      <th mat-header-cell *matHeaderCellDef>Statut</th>
      <td mat-cell *matCellDef="let i">
        <mat-chip [selected]="true" [color]="statusColor(i.status)">
          {{ i.status==='APPROVED' ? 'Approuvée' : i.status==='PENDING' ? 'En attente' : 'Refusée' }}
        </mat-chip>
      </td>
    </ng-container>

    <tr mat-header-row *matHeaderRowDef="['employee','type','period','days','status']"></tr>
    <tr mat-row *matRowDef="let row; columns: ['employee','type','period','days','status'];"></tr>
  </table>
</mat-card>

<style>
  .row{display:flex;align-items:center;gap:12px}
  .flex{flex:1 1 auto}
  .mb{margin-bottom:16px}
  .full{width:100%}
</style>
