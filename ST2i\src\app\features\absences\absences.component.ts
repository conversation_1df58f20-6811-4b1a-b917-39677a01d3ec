import { Component } from '@angular/core';

type AbsenceStatus = 'PENDING'|'APPROVED'|'REJECTED';
interface AbsenceItem {
  id: string;
  employee: string;
  type: 'ANNUAL_LEAVE'|'SICK_LEAVE'|'UNPAID_LEAVE'|'OTHER';
  from: string; to: string;
  days: number;
  status: AbsenceStatus;
}

@Component({
  selector: 'app-absences',
  templateUrl: './absences.component.html',
})
export class AbsencesComponent {
  items: AbsenceItem[] = [
    { id:'a1', employee:'<PERSON>', type:'ANNUAL_LEAVE', from:'2024-03-15', to:'2024-03-20', days:6, status:'PENDING' },
    { id:'a2', employee:'<PERSON>', type:'SICK_LEAVE',   from:'2024-03-22', to:'2024-03-23', days:2, status:'APPROVED' },
  ];

  statusColor(s: AbsenceStatus) {
    return s==='APPROVED' ? 'primary' : s==='PENDING' ? undefined : 'warn';
  }
}
