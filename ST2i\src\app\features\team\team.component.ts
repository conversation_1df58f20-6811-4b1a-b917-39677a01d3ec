import { Component } from '@angular/core';

interface Member {
  name: string;
  email: string;
  role: 'Admin'|'Manager'|'Employ<PERSON>';
  online: boolean;
  initials: string;
}

@Component({
  selector: 'app-team',
  templateUrl: './team.component.html',
})
export class TeamComponent {
  q = '';
  members: Member[] = [
    { name:'<PERSON>',   email:'<EMAIL>', role:'Admin',   online:true,  initials:'<PERSON><PERSON>' },
    { name:'<PERSON>',  email:'<EMAIL>', role:'Manager', online:true,  initials:'<PERSON>' },
    { name:'<PERSON>', email:'<EMAIL>', role:'Employ<PERSON>', online:false, initials:'PM' },
    { name:'<PERSON>',     email:'<EMAIL>',    role:'<PERSON>ploy<PERSON>', online:true,  initials:'N<PERSON>' },
  ];

  get filtered() {
    const q = this.q.toLowerCase();
    return this.members.filter(m => !q || m.name.toLowerCase().includes(q) || m.email.toLowerCase().includes(q));
  }

  add() { /* placeholder */ }
}
