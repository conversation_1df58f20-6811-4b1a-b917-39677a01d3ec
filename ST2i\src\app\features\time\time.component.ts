import { Component } from '@angular/core';

interface TimeEntry {
  project: string;
  task: string;
  hours: number;
  date: string; // ISO
}

@Component({
  selector: 'app-time',
  templateUrl: './time.component.html',
})
export class TimeComponent {
  running = false;
  startAt?: number;
  elapsed = 0; // ms
  timer?: any;

  quick = { project: '', task: '' };
  entries: TimeEntry[] = [
    { project:'Migration SI', task:'Analyse', hours:2.5, date:'2024-03-21' },
    { project:'Support', task:'Tickets', hours:1.5, date:'2024-03-21' },
  ];

  get display() {
    const s = Math.floor(this.elapsed / 1000);
    const hh = String(Math.floor(s/3600)).padStart(2,'0');
    const mm = String(Math.floor((s%3600)/60)).padStart(2,'0');
    const ss = String(s%60).padStart(2,'0');
    return `${hh}:${mm}:${ss}`;
  }

  start() {
    if (this.running) return;
    this.running = true;
    this.startAt = Date.now() - this.elapsed;
    this.timer = setInterval(()=>{ this.elapsed = Date.now() - (this.startAt as number); }, 250);
  }

  stop() {
    if (!this.running) return;
    clearInterval(this.timer);
    this.running = false;
  }

  reset() { this.stop(); this.elapsed = 0; }

  addQuick() {
    if (!this.quick.project || !this.quick.task) return;
    this.entries.unshift({
      project: this.quick.project,
      task: this.quick.task,
      hours: +(this.elapsed/3600000).toFixed(2),
      date: new Date().toISOString().slice(0,10)
    });
    this.quick = { project:'', task:'' };
    this.reset();
  }

  weekTotal() {
    return this.entries.reduce((a,e)=>a+e.hours,0);
  }
}
