<div class="auth">
  <h2>Login</h2>
  <p class="err" *ngIf="error">{{ error }}</p>

  <form (ngSubmit)="handleSubmit()">
    <mat-form-field appearance="fill" class="w">
      <mat-label>Nom d'utilisateur</mat-label>
      <input matInput [(ngModel)]="form.username" name="username" required>
    </mat-form-field>

    <mat-form-field appearance="fill" class="w">
      <mat-label>Mot de passe</mat-label>
      <input matInput [(ngModel)]="form.password" name="password" type="password" required>
    </mat-form-field>

    <button mat-raised-button color="primary" class="w" [disabled]="loading">
      {{ loading ? 'Connexion…' : 'Login' }}
    </button>
  </form>

  <p class="alt">Pas de compte ? <a routerLink="/signup">Register</a></p>
</div>
