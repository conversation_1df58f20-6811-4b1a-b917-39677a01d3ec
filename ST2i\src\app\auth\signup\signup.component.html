<div class="auth">
  <h2><PERSON><PERSON><PERSON> un compte</h2>
  <p class="ok" *ngIf="ok">{{ ok }}</p>
  <p class="err" *ngIf="err">{{ err }}</p>

  <form [formGroup]="form" (ngSubmit)="submit()">
    <mat-form-field appearance="fill" class="w">
      <mat-label>Nom d'utilisateur</mat-label>
      <input matInput formControlName="username" required>
    </mat-form-field>

    <mat-form-field appearance="fill" class="w">
      <mat-label>Email</mat-label>
      <input matInput type="email" formControlName="email" required>
    </mat-form-field>

    <mat-form-field appearance="fill" class="w">
      <mat-label>Mot de passe</mat-label>
      <input matInput type="password" formControlName="password" required>
    </mat-form-field>

    <button mat-raised-button color="primary" class="w" [disabled]="loading">
      {{ loading ? 'Enregistrement…' : 'Register' }}
    </button>
  </form>

  <p class="alt">Déjà inscrit ? <a routerLink="/login">Login</a></p>
</div>
