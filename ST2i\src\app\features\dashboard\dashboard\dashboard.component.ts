import { Component, OnInit } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { environment } from '../../../environments/environment';

@Component({
  selector: 'app-dashboard',
  templateUrl: './dashboard.component.html',
  styleUrls: ['./dashboard.component.css']
})
export class DashboardComponent implements OnInit {
  stats: any = {};
  year = new Date().getFullYear();
  month = new Date().getMonth() + 1;

  constructor(private http: HttpClient) {}
  ngOnInit() { this.load(); }

  load() {
    this.http.get(`${environment.api}/dashboard/stats`, { params: { year: this.year, month: this.month }})
      .subscribe(res => this.stats = res || {});
  }
}
