import { NgModule } from '@angular/core';
import { BrowserModule } from '@angular/platform-browser';
import { BrowserAnimationsModule } from '@angular/platform-browser/animations';
import { HttpClientModule } from '@angular/common/http';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { RouterModule } from '@angular/router';

import { routes } from './app.routes';
import { AppComponent } from './app.component';

// Auth
import { LoginComponent } from './auth/login/login.component';
import { SignupComponent } from './auth/signup/signup.component';

// Features
import { DashboardComponent } from './features/dashboard/dashboard/dashboard.component';
import { RequestsComponent } from './features/requests/requests.component';
import { TimeComponent } from './features/time/time.component';
import { AbsencesComponent } from './features/absences/absences.component';
import { TeamComponent } from './features/team/team.component';

// Angular Material (tout ce que tes templates utilisent)
import { MatCardModule } from '@angular/material/card';
import { MatIconModule } from '@angular/material/icon';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatButtonModule } from '@angular/material/button';
import { MatTableModule } from '@angular/material/table';
import { MatChipsModule } from '@angular/material/chips';
import { MatButtonToggleModule } from '@angular/material/button-toggle';
import { MatSidenavModule } from '@angular/material/sidenav';
import { MatListModule } from '@angular/material/list';
import { MatToolbarModule } from '@angular/material/toolbar';

@NgModule({
  declarations: [
    AppComponent,
    // auth
    LoginComponent, SignupComponent,
    // features
    DashboardComponent, RequestsComponent, TimeComponent, AbsencesComponent, TeamComponent,
  ],
  imports: [
    BrowserModule, BrowserAnimationsModule, HttpClientModule,
    FormsModule, ReactiveFormsModule,
    RouterModule.forRoot(routes),

    // Material
    MatCardModule, MatIconModule, MatFormFieldModule, MatInputModule, MatButtonModule,
    MatTableModule, MatChipsModule, MatButtonToggleModule,
    MatSidenavModule, MatListModule, MatToolbarModule,
  ],
  bootstrap: [AppComponent]
})
export class AppModule { }
