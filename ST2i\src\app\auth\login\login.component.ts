import { Component } from '@angular/core';
import { Router } from '@angular/router';
import { AuthService } from '../../core/auth.service';

@Component({
  selector: 'app-login',
  templateUrl: './login.component.html',
  styleUrls: ['./auth.css']
})
export class LoginComponent {
  form = { username: '', password: '' };
  loading = false; error = '';

  constructor(private auth: AuthService, private router: Router) {}

  handleSubmit() {
    this.error = ''; this.loading = true;
    this.auth.login(this.form.username, this.form.password).subscribe({
      next: () => { this.loading = false; this.router.navigate(['/dashboard']); },
      error: () => { this.loading = false; this.error = 'Identifiants invalides'; }
    });
  }
}
