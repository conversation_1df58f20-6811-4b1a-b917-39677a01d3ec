import { Component } from '@angular/core';

type RequestStatus = 'pending' | 'approved' | 'rejected';
interface RequestItem {
  id: string;
  type: 'Congé annuel' | 'Télétravail' | 'Sortie';
  requester: string;
  submittedAt: string;      // ISO date ou texte
  from: string;             // ISO date
  to: string;               // ISO date
  note?: string;
  status: RequestStatus;
}

@Component({
  selector: 'app-requests',
  templateUrl: './requests.component.html',
})
export class RequestsComponent {
  tab: 'all'|'pending'|'approved' = 'all';

  mockRequests: RequestItem[] = [
    { id:'r1', type:'Congé annuel', requester:'<PERSON>',
      submittedAt:'2024-03-10', from:'2024-03-15', to:'2024-03-20',
      note:'Vacances familiales', status:'pending' },
    { id:'r2', type:'Télétravail', requester:'<PERSON>',
      submittedAt:'2024-03-08', from:'2024-03-22', to:'2024-03-22',
      note:'Rendez-vous médical', status:'approved' },
    { id:'r3', type:'Sortie', requester:'<PERSON>',
      submittedAt:'2024-03-06', from:'2024-03-12', to:'2024-03-12',
      note:'Formalités', status:'pending' },
  ];

  get filtered() {
    if (this.tab==='all') return this.mockRequests;
    if (this.tab==='pending') return this.mockRequests.filter(r=>r.status==='pending');
    return this.mockRequests.filter(r=>r.status==='approved');
  }

  approve(r: RequestItem) { r.status = 'approved'; }
  reject(r: RequestItem)  { r.status = 'rejected'; }
}
