<h1>Gestion des demandes</h1>

<mat-card class="mb">
  <div class="row">
    <mat-form-field appearance="outline" style="flex:1">
      <mat-label>Rechercher</mat-label>
      <input matInput placeholder="par nom, type... (mock)">
    </mat-form-field>

    <mat-button-toggle-group [(ngModel)]="tab" class="ml">
      <mat-button-toggle value="all">Toutes</mat-button-toggle>
      <mat-button-toggle value="pending">En attente</mat-button-toggle>
      <mat-button-toggle value="approved">Approuvées</mat-button-toggle>
    </mat-button-toggle-group>

    <span class="flex"></span>
    <button mat-raised-button color="primary"><mat-icon>add</mat-icon> Nouvelle demande</button>
  </div>
</mat-card>

<ng-container *ngFor="let r of filtered">
  <mat-card class="mb">
    <div class="row">
      <div>
        <div class="title">
          <mat-icon class="mr">description</mat-icon> {{ r.type }}
          <mat-chip-set class="ml">
            <mat-chip [color]="r.status==='approved' ? 'primary' : (r.status==='pending' ? undefined : 'warn')" [selected]="true">
              {{ r.status==='approved' ? 'Approuvé' : r.status==='pending' ? 'En attente' : 'Refusé' }}
            </mat-chip>
          </mat-chip-set>
        </div>
        <div class="muted">
          {{ r.requester }} • Du {{ r.from }} au {{ r.to }} • Soumis le {{ r.submittedAt }}
        </div>
        <div *ngIf="r.note" class="note">{{ r.note }}</div>
      </div>
      <span class="flex"></span>
      <div class="actions">
        <button mat-stroked-button color="primary" class="mr" (click)="approve(r)">Approuver</button>
        <button mat-stroked-button color="warn" (click)="reject(r)">Refuser</button>
      </div>
    </div>
  </mat-card>
</ng-container>

<style>
  .row{display:flex;align-items:center;gap:12px}
  .flex{flex:1 1 auto}
  .mb{margin-bottom:16px}
  .ml{margin-left:12px}
  .mr{margin-right:8px}
  .title{font-weight:700;font-size:16px;display:flex;align-items:center}
  .muted{color:#6b7280;margin-top:4px}
  .note{margin-top:6px}
  .actions button{min-width:120px}
</style>
