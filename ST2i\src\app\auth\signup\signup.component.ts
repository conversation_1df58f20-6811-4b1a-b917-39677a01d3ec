import { Component } from '@angular/core';
import { FormBuilder, Validators } from '@angular/forms';
import { AuthService } from '../../core/auth.service';

@Component({
  selector: 'app-signup',
  templateUrl: './signup.component.html',
  styleUrls: ['./auth.css']
})
export class SignupComponent {
  loading = false; ok=''; err='';
  form = this.fb.group({
    username: ['', [Validators.required, Validators.minLength(3)]],
    email: ['', [Validators.required, Validators.email]],
    password: ['', [Validators.required, Validators.minLength(6)]],
  });
  constructor(private fb: FormBuilder, private auth: AuthService) {}
  submit() {
    this.ok = this.err = '';
    if (this.form.invalid) return;
    this.loading = true;
    this.auth.signup(this.form.value as any).subscribe({
      next: (r:any)=>{ this.ok = r?.message || '<PERSON><PERSON><PERSON>'; this.loading=false; },
      error: (e)=>{ this.err = e?.error?.error || 'Erreur'; this.loading=false; }
    });
  }
}
